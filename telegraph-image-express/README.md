# Telegraph-Image Express

Telegraph-Image 项目的 Express.js 本地部署版本。

## 功能特性

- 🖼️ 图片/视频/文档上传到 Telegram 频道
- 🔗 文件代理访问和 URL 生成
- 🛡️ 黑白名单管理和内容审核
- 👨‍💼 管理后台界面
- 🔐 Basic Auth 认证保护

## 快速开始

### 1. 环境配置

复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的环境变量：
- `TELEGRAM_BOT_TOKEN`: Telegram Bot Token
- `TELEGRAM_CHAT_ID`: Telegram 频道 ID

### 2. 安装依赖

```bash
npm install
```

### 3. 启动服务

```bash
npm start
```

服务将在 http://localhost:3000 启动

## 环境变量说明

| 变量名 | 必需 | 说明 |
|--------|------|------|
| TELEGRAM_BOT_TOKEN | ✅ | Telegram Bot Token |
| TELEGRAM_CHAT_ID | ✅ | Telegram 频道 ID |
| ADMIN_USERNAME | ❌ | 管理后台用户名 |
| ADMIN_PASSWORD | ❌ | 管理后台密码 |
| PORT | ❌ | 服务端口 (默认: 3000) |

## 项目结构

```
telegraph-image-express/
├── src/
│   ├── app.js              # 应用入口
│   ├── routes/             # 路由处理
│   ├── services/           # 业务服务
│   └── middleware/         # 中间件
├── public/                 # 静态文件
├── data/                   # 数据存储
└── package.json
```

## 开发说明

本项目从 Cloudflare Pages 版本重构而来，保持 API 兼容性。