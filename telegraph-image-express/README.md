# Telegraph-Image Express

Telegraph-Image 项目的 Express.js 本地部署版本，完全重构自 Cloudflare Pages 版本。

## ✨ 功能特性

- 🖼️ **多媒体上传**: 支持图片、视频、音频、文档上传到 Telegram 频道
- 🔗 **文件代理**: 智能文件代理访问和 URL 生成
- 🛡️ **访问控制**: 黑白名单管理和内容审核
- 👨‍💼 **管理后台**: 完整的 Web 管理界面
- 🔐 **安全认证**: Basic Auth 认证保护
- 📊 **监控面板**: 健康检查和系统状态监控
- 🚀 **高性能**: Express.js 高性能 Web 框架

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）

**Linux/macOS:**
```bash
# 开发模式
./start-dev.sh

# 生产模式
./start-prod.sh
```

**Windows:**
```batch
start.bat
```

### 方法二：手动启动

#### 1. 环境配置

复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的环境变量：
```bash
# 必需配置
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here

# 可选配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_password
```

#### 2. 安装依赖

```bash
npm install
```

#### 3. 启动服务

```bash
# 开发模式
npm run dev

# 生产模式
npm run prod
```

服务将在 http://localhost:3000 启动

## 📋 环境变量说明

| 变量名 | 必需 | 默认值 | 说明 |
|--------|------|--------|------|
| `TELEGRAM_BOT_TOKEN` | ✅ | - | Telegram Bot Token（从 @BotFather 获取）|
| `TELEGRAM_CHAT_ID` | ✅ | - | Telegram 频道/群组 ID（频道使用负数 ID）|
| `ADMIN_USERNAME` | ❌ | - | 管理后台用户名（未设置则无需认证）|
| `ADMIN_PASSWORD` | ❌ | - | 管理后台密码 |
| `PORT` | ❌ | 3000 | 服务端口 |
| `NODE_ENV` | ❌ | development | 运行环境 |
| `WHITELIST_MODE` | ❌ | false | 白名单模式（仅白名单文件可访问）|
| `ALLOWED_ORIGINS` | ❌ | * | CORS 允许的源（逗号分隔）|
| `MODERATE_CONTENT_API_KEY` | ❌ | - | 内容审核 API 密钥 |

## 🏗️ 项目结构

```
telegraph-image-express/
├── src/
│   ├── app.js              # Express 应用入口
│   ├── routes/             # 路由处理
│   │   ├── upload.js       # 文件上传路由
│   │   ├── file.js         # 文件获取路由
│   │   └── api/manage/     # 管理 API 路由
│   ├── services/           # 业务服务
│   │   ├── telegram.js     # Telegram API 服务
│   │   └── storage.js      # 本地存储服务
│   └── middleware/         # 中间件
│       └── auth.js         # 认证中间件
├── public/                 # 静态文件
│   ├── index.html          # 主页面
│   ├── admin.html          # 管理后台
│   └── _nuxt/              # 前端资源
├── data/                   # 数据存储
│   └── metadata.json       # 文件元数据
├── start-dev.sh            # 开发启动脚本
├── start-prod.sh           # 生产启动脚本
├── start.bat               # Windows 启动脚本
└── package.json
```

## 🔧 API 接口

### 文件操作
- `POST /upload` - 文件上传
- `GET /file/:id` - 文件获取

### 管理 API
- `GET /api/manage/list` - 文件列表
- `POST /api/manage/white/:id` - 添加白名单
- `POST /api/manage/block/:id` - 添加黑名单
- `DELETE /api/manage/delete/:id` - 删除文件
- `GET /api/manage/check` - 认证检查

### 系统监控
- `GET /health` - 健康检查

## 🛠️ 开发说明

### 启动开发服务器
```bash
npm run dev
```

### 生产部署
```bash
npm run prod
```

### 健康检查
```bash
npm run health
```

### PM2 管理（生产环境推荐）
```bash
# 安装 PM2
npm install -g pm2

# 启动
npm run prod:script

# 查看日志
npm run logs

# 重启
npm run restart

# 停止
npm run stop
```

## 🔒 安全配置

1. **认证保护**: 设置 `ADMIN_USERNAME` 和 `ADMIN_PASSWORD` 保护管理后台
2. **CORS 配置**: 通过 `ALLOWED_ORIGINS` 限制跨域访问
3. **白名单模式**: 启用 `WHITELIST_MODE` 仅允许白名单文件访问
4. **内容审核**: 配置 `MODERATE_CONTENT_API_KEY` 启用自动内容审核

## 📝 使用说明

1. **文件上传**: 访问主页 http://localhost:3000 进行文件上传
2. **管理后台**: 访问 http://localhost:3000/admin.html 管理文件
3. **健康监控**: 访问 http://localhost:3000/health 查看系统状态

## 🔄 从 Cloudflare Pages 迁移

本项目完全兼容原 Cloudflare Pages 版本的 API 和功能：
- ✅ 保持相同的 API 接口
- ✅ 兼容原有的前端界面
- ✅ 支持所有原有功能
- ✅ 数据格式完全一致

## 📄 许可证

本项目采用 CC0-1.0 许可证，详见 [LICENSE](LICENSE) 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请提交 [Issue](https://github.com/cf-pages/Telegraph-Image/issues)。