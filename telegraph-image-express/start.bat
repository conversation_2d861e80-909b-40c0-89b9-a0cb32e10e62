@echo off
REM Telegraph-Image Express Windows Startup Script

echo 🚀 Starting Telegraph-Image Express...
echo ==============================================

REM Check if .env file exists
if not exist ".env" (
    echo ❌ .env file not found!
    echo 📝 Creating .env from template...
    copy .env.example .env
    echo ✅ .env file created. Please edit it with your configuration.
    echo ⚠️  You need to set TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID
    pause
    exit /b 1
)

REM Check if node_modules exists
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install
)

REM Set development environment
set NODE_ENV=development

REM Start the server
echo 🔄 Starting server...
echo 📝 Logs will be displayed below:
echo ==============================================

node src/app.js

pause