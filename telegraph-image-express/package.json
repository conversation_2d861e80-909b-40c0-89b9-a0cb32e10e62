{"name": "telegraph-image-express", "version": "1.0.0", "description": "Telegraph-Image Express.js implementation - Local deployment version", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "node --watch src/app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["telegraph", "image", "upload", "telegram", "express"], "author": "", "license": "CC0-1.0", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-session": "^1.17.3", "form-data": "^4.0.3", "fs-extra": "^11.1.1", "multer": "^1.4.5-lts.1", "node-fetch": "^2.7.0"}, "engines": {"node": ">=16.0.0"}}