#!/bin/bash

# Telegraph-Image Express Development Startup Script

echo "🚀 Starting Telegraph-Image Express in Development Mode..."
echo "=============================================="

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found!"
    echo "📝 Creating .env from template..."
    cp .env.example .env
    echo "✅ .env file created. Please edit it with your configuration."
    echo "⚠️  You need to set TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID"
    exit 1
fi

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Set development environment
export NODE_ENV=development

# Start the server with auto-restart
echo "🔄 Starting server with auto-restart..."
echo "📝 Logs will be displayed below:"
echo "=============================================="

if command -v nodemon &> /dev/null; then
    nodemon src/app.js
else
    echo "⚠️  nodemon not found, using node --watch (Node.js 18+)"
    node --watch src/app.js
fi