#!/usr/bin/env node

/**
 * Telegraph-Image Express Deployment Check Script
 * 检查部署环境和配置是否正确
 */

const fs = require('fs');
const path = require('path');
require('dotenv').config();

console.log('🔍 Telegraph-Image Express Deployment Check');
console.log('==========================================');

let issues = [];
let warnings = [];

// 检查必需文件
const requiredFiles = [
    'src/app.js',
    'package.json',
    '.env',
    'public/index.html',
    'public/admin.html'
];

console.log('\n📁 Checking required files...');
requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} - Missing`);
        issues.push(`Missing required file: ${file}`);
    }
});

// 检查环境变量
console.log('\n🔧 Checking environment variables...');
const requiredEnvVars = ['TELEGRAM_BOT_TOKEN', 'TELEGRAM_CHAT_ID'];
const optionalEnvVars = ['ADMIN_USERNAME', 'ADMIN_PASSWORD', 'PORT', 'NODE_ENV'];

requiredEnvVars.forEach(varName => {
    if (process.env[varName]) {
        console.log(`✅ ${varName}: Configured`);
    } else {
        console.log(`❌ ${varName}: Missing`);
        issues.push(`Missing required environment variable: ${varName}`);
    }
});

optionalEnvVars.forEach(varName => {
    if (process.env[varName]) {
        console.log(`✅ ${varName}: ${varName.includes('PASSWORD') ? '***' : process.env[varName]}`);
    } else {
        console.log(`⚠️  ${varName}: Not set (optional)`);
        warnings.push(`Optional environment variable not set: ${varName}`);
    }
});

// 检查依赖
console.log('\n📦 Checking dependencies...');
try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const nodeModulesExists = fs.existsSync('node_modules');
    
    if (nodeModulesExists) {
        console.log('✅ node_modules: Installed');
    } else {
        console.log('❌ node_modules: Not found');
        issues.push('Dependencies not installed. Run: npm install');
    }
    
    console.log(`✅ Package name: ${packageJson.name}`);
    console.log(`✅ Version: ${packageJson.version}`);
} catch (error) {
    console.log('❌ package.json: Invalid or missing');
    issues.push('Invalid or missing package.json');
}

// 检查数据目录
console.log('\n💾 Checking data directory...');
if (fs.existsSync('data')) {
    console.log('✅ data directory: Exists');
    
    if (fs.existsSync('data/metadata.json')) {
        console.log('✅ metadata.json: Exists');
    } else {
        console.log('⚠️  metadata.json: Will be created on first run');
        warnings.push('metadata.json will be created automatically');
    }
} else {
    console.log('⚠️  data directory: Will be created on first run');
    warnings.push('data directory will be created automatically');
}

// 检查端口
console.log('\n🌐 Checking port configuration...');
const port = process.env.PORT || 3000;
console.log(`✅ Port: ${port}`);

// 检查权限
console.log('\n🔐 Checking file permissions...');
try {
    fs.accessSync('src/app.js', fs.constants.R_OK);
    console.log('✅ File permissions: OK');
} catch (error) {
    console.log('❌ File permissions: Issues detected');
    issues.push('File permission issues detected');
}

// 总结
console.log('\n📊 Deployment Check Summary');
console.log('============================');

if (issues.length === 0) {
    console.log('🎉 All checks passed! Your deployment is ready.');
    
    if (warnings.length > 0) {
        console.log('\n⚠️  Warnings:');
        warnings.forEach(warning => console.log(`   • ${warning}`));
    }
    
    console.log('\n🚀 To start the server:');
    console.log('   Development: npm run dev');
    console.log('   Production:  npm run prod');
    console.log('   Or use:      ./start-dev.sh (Linux/macOS)');
    console.log('   Or use:      start.bat (Windows)');
    
} else {
    console.log('❌ Issues found that need to be resolved:');
    issues.forEach(issue => console.log(`   • ${issue}`));
    
    if (warnings.length > 0) {
        console.log('\n⚠️  Additional warnings:');
        warnings.forEach(warning => console.log(`   • ${warning}`));
    }
    
    console.log('\n🔧 Please fix the issues above before starting the server.');
    process.exit(1);
}

console.log('\n📚 For more information, see README.md');
console.log('==========================================');