/**
 * Basic Auth认证中间件
 * 兼容原项目的认证逻辑和响应格式
 */

/**
 * 解析Basic Auth头部
 * @param {string} authorization - Authorization头部值
 * @returns {Object} 包含user和pass的对象
 */
function parseBasicAuth(authorization) {
    if (!authorization) {
        throw new Error('Missing authorization header');
    }

    const [scheme, encoded] = authorization.split(' ');

    // Authorization头部必须以Basic开头，后跟空格
    if (!encoded || scheme !== 'Basic') {
        throw new Error('Malformed authorization header.');
    }

    try {
        // 解码base64值并执行unicode标准化
        const buffer = Buffer.from(encoded, 'base64');
        const decoded = buffer.toString('utf8').normalize();

        // 用户名和密码由第一个冒号分隔
        const index = decoded.indexOf(':');

        // 用户名和密码必须由第一个冒号分隔，且不能包含控制字符
        if (index === -1 || /[\0-\x1F\x7F]/.test(decoded)) {
            throw new Error('Invalid authorization value.');
        }

        return {
            user: decoded.substring(0, index),
            pass: decoded.substring(index + 1),
        };
    } catch (error) {
        throw new Error('Invalid authorization value.');
    }
}

/**
 * Basic Auth认证中间件
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express next函数
 */
function basicAuth(req, res, next) {
    try {
        // 检查环境变量
        const adminUsername = process.env.ADMIN_USERNAME;
        const adminPassword = process.env.ADMIN_PASSWORD;

        console.log('Admin username configured:', !!adminUsername);

        // 如果未设置用户名，跳过认证
        if (!adminUsername || adminUsername === '') {
            console.log('No admin username configured, skipping authentication');
            return next();
        }

        // 检查Authorization头部
        const authorization = req.get('Authorization');

        if (!authorization) {
            console.log('No authorization header provided');
            return res.status(401)
                .set('WWW-Authenticate', 'Basic realm="Telegraph-Image Admin", charset="UTF-8"')
                .set('Content-Type', 'text/plain;charset=UTF-8')
                .set('Cache-Control', 'no-store')
                .send('You need to login.');
        }

        // 解析认证信息
        const { user, pass } = parseBasicAuth(authorization);

        // 验证用户名和密码
        if (adminUsername !== user || adminPassword !== pass) {
            console.log('Invalid credentials provided');
            return res.status(401)
                .set('Content-Type', 'text/plain;charset=UTF-8')
                .set('Cache-Control', 'no-store')
                .send('Invalid credentials.');
        }

        console.log('Authentication successful');
        next();

    } catch (error) {
        console.error('Authentication error:', error.message);
        return res.status(400)
            .set('Content-Type', 'text/plain;charset=UTF-8')
            .set('Cache-Control', 'no-store')
            .send(error.message);
    }
}

/**
 * 认证检查路由处理器
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
function checkAuth(req, res) {
    const adminUsername = process.env.ADMIN_USERNAME;
    
    if (!adminUsername || adminUsername === '') {
        return res.status(200)
            .set('Content-Type', 'text/plain;charset=UTF-8')
            .send('Not using basic auth.');
    } else {
        return res.status(200)
            .set('Content-Type', 'text/plain;charset=UTF-8')
            .send('true');
    }
}

module.exports = {
    basicAuth,
    checkAuth
};