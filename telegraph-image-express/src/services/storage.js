const fs = require('fs-extra');
const path = require('path');

class StorageService {
    constructor(dataDir = 'data') {
        this.dataDir = dataDir;
        this.metadataFile = path.join(dataDir, 'metadata.json');
        this.lockFile = path.join(dataDir, 'metadata.lock');
        this.lockTimeout = 5000; // 5秒锁超时
        
        // 确保数据目录存在
        this.ensureDataDir();
    }

    /**
     * 确保数据目录和元数据文件存在
     */
    async ensureDataDir() {
        try {
            await fs.ensureDir(this.dataDir);
            
            // 如果元数据文件不存在，创建空的JSON对象
            if (!await fs.pathExists(this.metadataFile)) {
                await fs.writeJson(this.metadataFile, {}, { spaces: 2 });
            }
        } catch (error) {
            console.error('Error ensuring data directory:', error);
        }
    }

    /**
     * 获取文件锁
     */
    async acquireLock() {
        const startTime = Date.now();
        
        while (Date.now() - startTime < this.lockTimeout) {
            try {
                // 尝试创建锁文件
                await fs.writeFile(this.lockFile, process.pid.toString(), { flag: 'wx' });
                return true;
            } catch (error) {
                if (error.code === 'EEXIST') {
                    // 锁文件已存在，检查是否过期
                    try {
                        const stats = await fs.stat(this.lockFile);
                        const lockAge = Date.now() - stats.mtime.getTime();
                        
                        // 如果锁文件超过10秒，认为是死锁，删除它
                        if (lockAge > 10000) {
                            await fs.remove(this.lockFile);
                            continue;
                        }
                    } catch (statError) {
                        // 锁文件可能已被删除，继续尝试
                        continue;
                    }
                    
                    // 等待50ms后重试
                    await new Promise(resolve => setTimeout(resolve, 50));
                } else {
                    throw error;
                }
            }
        }
        
        throw new Error('Failed to acquire lock within timeout');
    }    /**
     * 释放文件锁
     */
    async releaseLock() {
        try {
            await fs.remove(this.lockFile);
        } catch (error) {
            // 忽略锁文件不存在的错误
            if (error.code !== 'ENOENT') {
                console.error('Error releasing lock:', error);
            }
        }
    }

    /**
     * 读取元数据文件
     */
    async readMetadata() {
        try {
            return await fs.readJson(this.metadataFile);
        } catch (error) {
            console.error('Error reading metadata:', error);
            return {};
        }
    }

    /**
     * 写入元数据文件
     */
    async writeMetadata(data) {
        try {
            await fs.writeJson(this.metadataFile, data, { spaces: 2 });
        } catch (error) {
            console.error('Error writing metadata:', error);
            throw error;
        }
    }    /**
     * 保存文件元数据（兼容Cloudflare KV的put方法）
     * @param {string} key - 文件键名
     * @param {string} value - 文件值（通常为空字符串）
     * @param {Object} options - 选项，包含metadata
     * @returns {Promise<void>}
     */
    async put(key, value = "", options = {}) {
        await this.acquireLock();
        
        try {
            const data = await this.readMetadata();
            
            // 创建完整的记录结构
            data[key] = {
                value: value,
                metadata: {
                    TimeStamp: Date.now(),
                    ListType: "None",
                    Label: "None",
                    liked: false,
                    fileName: key,
                    fileSize: 0,
                    ...options.metadata
                }
            };
            
            await this.writeMetadata(data);
        } finally {
            await this.releaseLock();
        }
    }

    /**
     * 获取文件值（兼容Cloudflare KV的get方法）
     * @param {string} key - 文件键名
     * @returns {Promise<string|null>}
     */
    async get(key) {
        const data = await this.readMetadata();
        const record = data[key];
        return record ? record.value : null;
    }

    /**
     * 获取文件及元数据（兼容Cloudflare KV的getWithMetadata方法）
     * @param {string} key - 文件键名
     * @returns {Promise<{value: string, metadata: Object}|null>}
     */
    async getWithMetadata(key) {
        const data = await this.readMetadata();
        const record = data[key];
        
        if (!record) {
            return null;
        }
        
        return {
            value: record.value || "",
            metadata: record.metadata || {}
        };
    }    /**
     * 删除文件记录
     * @param {string} key - 文件键名
     * @returns {Promise<boolean>} 是否删除成功
     */
    async delete(key) {
        await this.acquireLock();
        
        try {
            const data = await this.readMetadata();
            
            if (data[key]) {
                delete data[key];
                await this.writeMetadata(data);
                return true;
            }
            
            return false;
        } finally {
            await this.releaseLock();
        }
    }

    /**
     * 列出文件记录
     * @param {Object} options - 选项
     * @param {number} options.limit - 限制数量
     * @param {number} options.offset - 偏移量
     * @param {string} options.search - 搜索关键词
     * @param {string} options.listType - 过滤ListType
     * @param {string} options.sortBy - 排序字段
     * @param {string} options.sortOrder - 排序顺序 (asc/desc)
     * @returns {Promise<{keys: Array, total: number}>}
     */
    async list(options = {}) {
        const {
            limit = 50,
            offset = 0,
            search = '',
            listType = '',
            sortBy = 'TimeStamp',
            sortOrder = 'desc'
        } = options;
        
        const data = await this.readMetadata();
        let records = Object.entries(data).map(([key, record]) => ({
            key,
            ...record
        }));
        
        // 搜索过滤
        if (search) {
            const searchLower = search.toLowerCase();
            records = records.filter(record => 
                record.key.toLowerCase().includes(searchLower) ||
                (record.metadata.fileName && record.metadata.fileName.toLowerCase().includes(searchLower))
            );
        }
        
        // ListType过滤
        if (listType) {
            records = records.filter(record => 
                record.metadata.ListType === listType
            );
        }
        
        // 排序
        records.sort((a, b) => {
            const aVal = a.metadata[sortBy] || 0;
            const bVal = b.metadata[sortBy] || 0;
            
            if (sortOrder === 'asc') {
                return aVal > bVal ? 1 : -1;
            } else {
                return aVal < bVal ? 1 : -1;
            }
        });
        
        const total = records.length;
        const keys = records.slice(offset, offset + limit);
        
        return { keys, total };
    }    /**
     * 更新文件元数据
     * @param {string} key - 文件键名
     * @param {Object} metadata - 要更新的元数据
     * @returns {Promise<boolean>} 是否更新成功
     */
    async updateMetadata(key, metadata) {
        await this.acquireLock();
        
        try {
            const data = await this.readMetadata();
            
            if (data[key]) {
                data[key].metadata = {
                    ...data[key].metadata,
                    ...metadata
                };
                await this.writeMetadata(data);
                return true;
            }
            
            return false;
        } finally {
            await this.releaseLock();
        }
    }

    /**
     * 获取统计信息
     * @returns {Promise<Object>} 统计信息
     */
    async getStats() {
        const data = await this.readMetadata();
        const records = Object.values(data);
        
        const stats = {
            total: records.length,
            whiteList: 0,
            blackList: 0,
            none: 0,
            liked: 0
        };
        
        records.forEach(record => {
            const metadata = record.metadata || {};
            
            switch (metadata.ListType) {
                case 'White':
                    stats.whiteList++;
                    break;
                case 'Block':
                    stats.blackList++;
                    break;
                default:
                    stats.none++;
            }
            
            if (metadata.liked) {
                stats.liked++;
            }
        });
        
        return stats;
    }

    /**
     * 清理过期数据（可选功能）
     * @param {number} maxAge - 最大年龄（毫秒）
     * @returns {Promise<number>} 清理的记录数
     */
    async cleanup(maxAge = 30 * 24 * 60 * 60 * 1000) { // 默认30天
        await this.acquireLock();
        
        try {
            const data = await this.readMetadata();
            const now = Date.now();
            let cleanedCount = 0;
            
            Object.keys(data).forEach(key => {
                const record = data[key];
                const timestamp = record.metadata?.TimeStamp || 0;
                
                if (now - timestamp > maxAge) {
                    delete data[key];
                    cleanedCount++;
                }
            });
            
            if (cleanedCount > 0) {
                await this.writeMetadata(data);
            }
            
            return cleanedCount;
        } finally {
            await this.releaseLock();
        }
    }
}

module.exports = StorageService;