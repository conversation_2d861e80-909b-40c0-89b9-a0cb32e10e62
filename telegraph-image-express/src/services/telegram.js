const fetch = require('node-fetch');
const FormData = require('form-data');

class TelegramService {
    constructor() {
        this.MAX_RETRIES = 2;
    }

    /**
     * 上传文件到Telegram频道
     * @param {Buffer|Stream} file - 文件数据
     * @param {string} fileName - 文件名
     * @param {string} mimeType - 文件MIME类型
     * @param {string} chatId - Telegram频道ID
     * @param {string} botToken - Telegram Bot Token
     * @returns {Promise<{success: boolean, data?: any, error?: string}>}
     */
    async uploadFile(file, fileName, mimeType, chatId, botToken) {
        const formData = new FormData();
        formData.append('chat_id', chatId);

        // 根据文件类型选择合适的上传方式
        let apiEndpoint;
        if (mimeType.startsWith('image/')) {
            formData.append('photo', file, { filename: fileName });
            apiEndpoint = 'sendPhoto';
        } else if (mimeType.startsWith('audio/')) {
            formData.append('audio', file, { filename: fileName });
            apiEndpoint = 'sendAudio';
        } else if (mimeType.startsWith('video/')) {
            formData.append('video', file, { filename: fileName });
            apiEndpoint = 'sendVideo';
        } else {
            formData.append('document', file, { filename: fileName });
            apiEndpoint = 'sendDocument';
        }

        return await this.sendToTelegram(formData, apiEndpoint, botToken, file, fileName, chatId);
    }    /**
     * 发送请求到Telegram API
     * @param {FormData} formData - 表单数据
     * @param {string} apiEndpoint - API端点
     * @param {string} botToken - Bot Token
     * @param {Buffer|Stream} originalFile - 原始文件（用于重试）
     * @param {string} fileName - 文件名（用于重试）
     * @param {string} chatId - 频道ID（用于重试）
     * @param {number} retryCount - 重试次数
     * @returns {Promise<{success: boolean, data?: any, error?: string}>}
     */
    async sendToTelegram(formData, apiEndpoint, botToken, originalFile, fileName, chatId, retryCount = 0) {
        const apiUrl = `https://api.telegram.org/bot${botToken}/${apiEndpoint}`;

        try {
            const response = await fetch(apiUrl, { 
                method: 'POST', 
                body: formData 
            });
            const responseData = await response.json();

            if (response.ok) {
                return { success: true, data: responseData };
            }

            // 图片上传失败时转为文档方式重试
            if (retryCount < this.MAX_RETRIES && apiEndpoint === 'sendPhoto') {
                console.log('Retrying image as document...');
                const newFormData = new FormData();
                newFormData.append('chat_id', chatId);
                newFormData.append('document', originalFile, { filename: fileName });
                return await this.sendToTelegram(newFormData, 'sendDocument', botToken, originalFile, fileName, chatId, retryCount + 1);
            }

            return {
                success: false,
                error: responseData.description || 'Upload to Telegram failed'
            };
        } catch (error) {
            console.error('Network error:', error);
            if (retryCount < this.MAX_RETRIES) {
                await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
                return await this.sendToTelegram(formData, apiEndpoint, botToken, originalFile, fileName, chatId, retryCount + 1);
            }
            return { success: false, error: 'Network error occurred' };
        }
    }    /**
     * 从Telegram响应中提取file_id
     * @param {Object} response - Telegram API响应
     * @returns {string|null} file_id
     */
    getFileId(response) {
        if (!response.ok || !response.result) return null;

        const result = response.result;
        if (result.photo) {
            // 选择最大尺寸的图片
            return result.photo.reduce((prev, current) =>
                (prev.file_size > current.file_size) ? prev : current
            ).file_id;
        }
        if (result.document) return result.document.file_id;
        if (result.video) return result.video.file_id;
        if (result.audio) return result.audio.file_id;

        return null;
    }

    /**
     * 获取文件下载路径
     * @param {string} fileId - Telegram文件ID
     * @param {string} botToken - Bot Token
     * @returns {Promise<string|null>} 文件路径
     */
    async getFilePath(fileId, botToken) {
        try {
            const url = `https://api.telegram.org/bot${botToken}/getFile?file_id=${fileId}`;
            const response = await fetch(url, {
                method: 'GET',
            });

            if (!response.ok) {
                console.error(`HTTP error! status: ${response.status}`);
                return null;
            }

            const responseData = await response.json();
            const { ok, result } = responseData;

            if (ok && result) {
                return result.file_path;
            } else {
                console.error('Error in response data:', responseData);
                return null;
            }
        } catch (error) {
            console.error('Error fetching file path:', error.message);
            return null;
        }
    }    /**
     * 获取文件完整下载URL
     * @param {string} filePath - 文件路径
     * @param {string} botToken - Bot Token
     * @returns {string} 完整的文件URL
     */
    getFileUrl(filePath, botToken) {
        return `https://api.telegram.org/file/bot${botToken}/${filePath}`;
    }

    /**
     * 代理获取文件内容
     * @param {string} fileId - 文件ID
     * @param {string} botToken - Bot Token
     * @returns {Promise<Response|null>} 文件响应
     */
    async proxyFile(fileId, botToken) {
        try {
            const filePath = await this.getFilePath(fileId, botToken);
            if (!filePath) {
                return null;
            }

            const fileUrl = this.getFileUrl(filePath, botToken);
            const response = await fetch(fileUrl);
            
            if (!response.ok) {
                console.error(`Failed to fetch file: ${response.status}`);
                return null;
            }

            return response;
        } catch (error) {
            console.error('Error proxying file:', error.message);
            return null;
        }
    }
}

module.exports = TelegramService;