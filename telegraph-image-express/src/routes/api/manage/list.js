const express = require('express');
const { basicAuth } = require('../../../middleware/auth');
const StorageService = require('../../../services/storage');

const router = express.Router();
const storageService = new StorageService();

/**
 * GET /api/manage/list - 文件列表接口
 * 返回所有文件的元数据列表，支持分页和搜索
 */
router.get('/', basicAuth, async (req, res) => {
    try {
        console.log('Getting file list...');
        
        // 获取查询参数
        const {
            page = 1,
            limit = 50,
            search = '',
            listType = '',
            sortBy = 'TimeStamp',
            sortOrder = 'desc'
        } = req.query;
        
        // 计算偏移量
        const offset = (parseInt(page) - 1) * parseInt(limit);
        
        // 获取文件列表
        const result = await storageService.list({
            limit: parseInt(limit),
            offset: offset,
            search: search,
            listType: listType,
            sortBy: sortBy,
            sortOrder: sortOrder
        });
        
        console.log(`Found ${result.total} files, returning ${result.keys.length}`);
        
        // 转换为原项目兼容的格式
        const compatibleKeys = result.keys.map(record => ({
            name: record.key,
            metadata: {
                TimeStamp: record.metadata.TimeStamp || Date.now(),
                ListType: record.metadata.ListType || "None",
                Label: record.metadata.Label || "None",
                liked: record.metadata.liked || false,
                fileName: record.metadata.fileName || record.key,
                fileSize: record.metadata.fileSize || 0
            }
        }));
        
        // 返回与原项目完全一致的格式
        return res.status(200).json(compatibleKeys);
        
    } catch (error) {
        console.error('List API error:', error);
        return res.status(500).json({ 
            error: 'Internal server error' 
        });
    }
});

module.exports = router;