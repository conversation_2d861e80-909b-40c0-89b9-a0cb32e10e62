const express = require('express');
const { basicAuth } = require('../../../middleware/auth');
const StorageService = require('../../../services/storage');

const router = express.Router();
const storageService = new StorageService();

/**
 * DELETE /api/manage/delete/:id - 删除文件记录
 * 注意：这只删除元数据记录，不删除Telegram中的实际文件
 */
router.delete('/:id', basicAuth, async (req, res) => {
    try {
        const fileId = req.params.id;
        console.log('Deleting file record:', fileId);
        
        // 删除文件记录
        const deleted = await storageService.delete(fileId);
        
        if (deleted) {
            console.log('File record deleted successfully');
            // 返回删除的文件ID（与原项目格式一致）
            return res.status(200).json(fileId);
        } else {
            console.log('File record not found');
            return res.status(404).json({ 
                error: 'File not found' 
            });
        }
        
    } catch (error) {
        console.error('Delete API error:', error);
        return res.status(500).json({ 
            error: 'Internal server error' 
        });
    }
});

module.exports = router;