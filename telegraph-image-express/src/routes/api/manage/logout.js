const express = require('express');

const router = express.Router();

/**
 * POST /api/manage/logout - 登出处理
 * 返回401状态码强制浏览器清除认证信息
 */
router.post('/', (req, res) => {
    try {
        console.log('Logout requested');
        
        // 返回401状态码，与原项目行为一致
        // 这会强制浏览器清除Basic Auth认证信息
        return res.status(401)
            .set('Content-Type', 'text/plain;charset=UTF-8')
            .send('Logged out.');
        
    } catch (error) {
        console.error('Logout API error:', error);
        return res.status(500).json({ 
            error: 'Internal server error' 
        });
    }
});

module.exports = router;