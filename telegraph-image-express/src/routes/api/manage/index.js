const express = require('express');

const router = express.Router();

// 导入各个管理API路由
const checkRouter = require('./check');
const listRouter = require('./list');
const whiteRouter = require('./white');
const blockRouter = require('./block');
const deleteRouter = require('./delete');
const loginRouter = require('./login');
const logoutRouter = require('./logout');

// 注册路由
router.use('/check', checkRouter);
router.use('/list', listRouter);
router.use('/white', whiteRouter);
router.use('/block', blockRouter);
router.use('/delete', deleteRouter);
router.use('/login', loginRouter);
router.use('/logout', logoutRouter);

module.exports = router;