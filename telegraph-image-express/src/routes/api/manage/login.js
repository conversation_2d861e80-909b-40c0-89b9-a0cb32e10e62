const express = require('express');
const { basicAuth } = require('../../../middleware/auth');

const router = express.Router();

/**
 * POST /api/manage/login - 登录重定向
 * 验证认证后重定向到管理页面
 */
router.post('/', basicAuth, (req, res) => {
    try {
        console.log('Login successful, redirecting to admin page');
        
        // 获取请求的origin
        const origin = `${req.protocol}://${req.get('host')}`;
        
        // 重定向到管理页面（与原项目行为一致）
        return res.redirect(302, `${origin}/admin.html`);
        
    } catch (error) {
        console.error('Login API error:', error);
        return res.status(500).json({ 
            error: 'Internal server error' 
        });
    }
});

module.exports = router;