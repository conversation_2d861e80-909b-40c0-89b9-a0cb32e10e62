const express = require('express');
const { basicAuth } = require('../../../middleware/auth');
const StorageService = require('../../../services/storage');

const router = express.Router();
const storageService = new StorageService();

/**
 * POST /api/manage/block/:id - 添加文件到黑名单
 */
router.post('/:id', basicAuth, async (req, res) => {
    try {
        const fileId = req.params.id;
        console.log('Adding to blacklist:', fileId);
        
        // 获取现有元数据
        let record = await storageService.getWithMetadata(fileId);
        
        if (!record || !record.metadata) {
            // 如果没有元数据，创建默认元数据
            console.log("Metadata not found, creating default...");
            record = {
                metadata: {
                    TimeStamp: Date.now(),
                    ListType: "None",
                    Label: "None",
                    liked: false,
                    fileName: fileId,
                    fileSize: 0,
                }
            };
        }
        
        // 更新ListType为Block
        record.metadata.ListType = "Block";
        
        // 保存更新的元数据
        await storageService.put(fileId, "", { metadata: record.metadata });
        
        console.log('File added to blacklist successfully');
        
        // 返回更新后的元数据（与原项目格式一致）
        return res.status(200).json(record.metadata);
        
    } catch (error) {
        console.error('Block API error:', error);
        return res.status(500).json({ 
            error: 'Internal server error' 
        });
    }
});

module.exports = router;