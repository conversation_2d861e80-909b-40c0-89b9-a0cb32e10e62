const express = require('express');
const TelegramService = require('../services/telegram');
const StorageService = require('../services/storage');
const fetch = require('node-fetch');

const router = express.Router();

// 初始化服务
const telegramService = new TelegramService();
const storageService = new StorageService();

/**
 * GET /file/:id - 文件获取代理接口
 */
router.get('/:id', async (req, res) => {
    try {
        const fileId = req.params.id;
        const url = req.originalUrl;
        
        console.log(`File request: ${fileId}`);
        
        // 解析文件ID和扩展名
        let actualFileId = fileId;
        let fileExtension = '';
        
        if (fileId.includes('.')) {
            const parts = fileId.split('.');
            fileExtension = parts.pop();
            actualFileId = parts.join('.');
        }
        
        // 验证环境变量
        const botToken = process.env.TELEGRAM_BOT_TOKEN;
        if (!botToken) {
            return res.status(500).json({ 
                error: 'Server configuration error: Missing Telegram bot token' 
            });
        }
        
        // 检查是否为管理员访问
        const referer = req.get('Referer') || '';
        const isAdmin = referer.includes('/admin');
        
        console.log(`Admin access: ${isAdmin}, Referer: ${referer}`);
        
        // 从Telegram获取文件
        let fileResponse;
        
        // 检查路径长度，判断是否为Telegram Bot API上传的文件
        if (actualFileId.length > 39) {
            // 通过Telegram Bot API获取文件
            fileResponse = await telegramService.proxyFile(actualFileId, botToken);
        } else {
            // 尝试从Telegraph获取（兼容旧文件）
            const telegraphUrl = `https://telegra.ph${url}`;
            try {
                fileResponse = await fetch(telegraphUrl);
            } catch (error) {
                console.error('Telegraph fetch error:', error);
                return res.status(404).json({ error: 'File not found' });
            }
        }
        
        if (!fileResponse || !fileResponse.ok) {
            console.error('Failed to fetch file from source');
            return res.status(404).json({ error: 'File not found' });
        }        // 如果是管理员访问，直接返回文件
        if (isAdmin) {
            console.log('Admin access, returning file directly');
            return pipeResponse(fileResponse, res);
        }
        
        // 获取文件元数据进行访问控制
        let record = await storageService.getWithMetadata(fileId);
        
        if (!record || !record.metadata) {
            // 如果没有元数据，初始化默认元数据
            console.log("Metadata not found, initializing...");
            record = {
                metadata: {
                    ListType: "None",
                    Label: "None",
                    TimeStamp: Date.now(),
                    liked: false,
                    fileName: fileId,
                    fileSize: 0,
                }
            };
            
            try {
                await storageService.put(fileId, "", { metadata: record.metadata });
            } catch (error) {
                console.error('Failed to save initial metadata:', error);
            }
        }
        
        const metadata = {
            ListType: record.metadata.ListType || "None",
            Label: record.metadata.Label || "None",
            TimeStamp: record.metadata.TimeStamp || Date.now(),
            liked: record.metadata.liked !== undefined ? record.metadata.liked : false,
            fileName: record.metadata.fileName || fileId,
            fileSize: record.metadata.fileSize || 0,
        };
        
        console.log(`File metadata - ListType: ${metadata.ListType}, Label: ${metadata.Label}`);
        
        // 处理白名单
        if (metadata.ListType === "White") {
            console.log('File is whitelisted, returning directly');
            return pipeResponse(fileResponse, res);
        }
        
        // 处理黑名单和成人内容
        if (metadata.ListType === "Block" || metadata.Label === "adult") {
            console.log('File is blocked or adult content');
            const redirectUrl = referer ? 
                "https://static-res.pages.dev/teleimage/img-block-compressed.png" : 
                "/block-img.html";
            return res.redirect(302, redirectUrl);
        }
        
        // 检查白名单模式
        if (process.env.WHITELIST_MODE === "true") {
            console.log('WhiteList mode enabled, redirecting');
            return res.redirect(302, "/whitelist-on.html");
        }        // 内容审核（可选）
        if (process.env.MODERATE_CONTENT_API_KEY) {
            try {
                console.log("Starting content moderation...");
                const moderateUrl = `https://api.moderatecontent.com/moderate/?key=${process.env.MODERATE_CONTENT_API_KEY}&url=https://telegra.ph${url}`;
                const moderateResponse = await fetch(moderateUrl);
                
                if (moderateResponse.ok) {
                    const moderateData = await moderateResponse.json();
                    console.log("Content moderation results:", moderateData);
                    
                    if (moderateData && moderateData.rating_label) {
                        // 更新元数据中的Label
                        metadata.Label = moderateData.rating_label;
                        
                        if (moderateData.rating_label === "adult") {
                            console.log("Content marked as adult, saving metadata and redirecting");
                            
                            try {
                                await storageService.updateMetadata(fileId, { Label: "adult" });
                            } catch (error) {
                                console.error('Failed to update metadata:', error);
                            }
                            
                            const redirectUrl = referer ? 
                                "https://static-res.pages.dev/teleimage/img-block-compressed.png" : 
                                "/block-img.html";
                            return res.redirect(302, redirectUrl);
                        }
                    }
                } else {
                    console.error("Content moderation API request failed:", moderateResponse.status);
                }
            } catch (error) {
                console.error("Error during content moderation:", error.message);
                // 审核失败不应影响用户体验，继续处理
            }
        }
        
        // 保存/更新元数据
        try {
            await storageService.updateMetadata(fileId, metadata);
            console.log("Metadata updated successfully");
        } catch (error) {
            console.error('Failed to update metadata:', error);
        }
        
        // 返回文件内容
        console.log('Returning file content');
        return pipeResponse(fileResponse, res);
        
    } catch (error) {
        console.error('File route error:', error);
        return res.status(500).json({ 
            error: 'Internal server error' 
        });
    }
});

/**
 * 将文件响应管道传输到Express响应
 */
function pipeResponse(sourceResponse, targetResponse) {
    // 复制响应头
    sourceResponse.headers.forEach((value, key) => {
        // 跳过一些不需要的头部
        if (!['content-encoding', 'transfer-encoding', 'connection'].includes(key.toLowerCase())) {
            targetResponse.set(key, value);
        }
    });
    
    // 设置状态码
    targetResponse.status(sourceResponse.status);
    
    // 管道传输响应体
    sourceResponse.body.pipe(targetResponse);
}

module.exports = router;