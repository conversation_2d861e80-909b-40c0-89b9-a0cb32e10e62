const express = require('express');
const multer = require('multer');
const TelegramService = require('../services/telegram');
const StorageService = require('../services/storage');

const router = express.Router();

// 配置multer用于处理文件上传
const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 20 * 1024 * 1024, // 20MB限制（Telegram Bot API的最大文件大小）
        files: 1 // 一次只能上传一个文件
    },
    fileFilter: (req, file, cb) => {
        // 允许的文件类型
        const allowedTypes = [
            'image/jpeg', 'image/png', 'image/gif', 'image/webp',
            'video/mp4', 'video/avi', 'video/mov', 'video/wmv',
            'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a',
            'application/pdf', 'text/plain', 'application/zip'
        ];
        
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error(`File type ${file.mimetype} not allowed`), false);
        }
    }
});

// 初始化服务
const telegramService = new TelegramService();
const storageService = new StorageService();

/**
 * POST /upload - 文件上传接口
 */
router.post('/', (req, res, next) => {
    upload.single('file')(req, res, (err) => {
        if (err) {
            console.error('Multer error:', err);
            
            if (err instanceof multer.MulterError) {
                if (err.code === 'LIMIT_FILE_SIZE') {
                    return res.status(400).json({ 
                        error: 'File too large. Maximum size is 20MB.' 
                    });
                }
                if (err.code === 'LIMIT_FILE_COUNT') {
                    return res.status(400).json({ 
                        error: 'Too many files. Only one file allowed per upload.' 
                    });
                }
            }
            
            if (err.message.includes('not allowed')) {
                return res.status(400).json({ 
                    error: err.message 
                });
            }
            
            return res.status(500).json({ 
                error: 'File upload error: ' + err.message 
            });
        }
        
        next();
    });
}, async (req, res) => {
    try {
        // 验证上传文件存在
        if (!req.file) {
            return res.status(400).json({ error: 'No file uploaded' });
        }

        const uploadFile = req.file;
        const fileName = uploadFile.originalname;
        const fileExtension = fileName.split('.').pop().toLowerCase();
        const mimeType = uploadFile.mimetype;

        // 验证环境变量
        const botToken = process.env.TELEGRAM_BOT_TOKEN;
        const chatId = process.env.TELEGRAM_CHAT_ID;

        if (!botToken || !chatId) {
            return res.status(500).json({ 
                error: 'Server configuration error: Missing Telegram credentials' 
            });
        }        // 上传文件到Telegram
        console.log(`Uploading file: ${fileName} (${mimeType}, ${uploadFile.size} bytes)`);
        
        const uploadResult = await telegramService.uploadFile(
            uploadFile.buffer,
            fileName,
            mimeType,
            chatId,
            botToken
        );

        if (!uploadResult.success) {
            console.error('Telegram upload failed:', uploadResult.error);
            return res.status(500).json({ 
                error: uploadResult.error || 'Failed to upload to Telegram' 
            });
        }

        // 从Telegram响应中提取file_id
        const fileId = telegramService.getFileId(uploadResult.data);
        
        if (!fileId) {
            console.error('Failed to get file ID from Telegram response');
            return res.status(500).json({ 
                error: 'Failed to get file ID from upload response' 
            });
        }

        console.log(`File uploaded successfully, file_id: ${fileId}`);

        // 保存文件元数据到本地存储
        const fileKey = `${fileId}.${fileExtension}`;
        
        try {
            await storageService.put(fileKey, "", {
                metadata: {
                    TimeStamp: Date.now(),
                    ListType: "None",
                    Label: "None",
                    liked: false,
                    fileName: fileName,
                    fileSize: uploadFile.size,
                }
            });
            
            console.log(`Metadata saved for: ${fileKey}`);
        } catch (storageError) {
            console.error('Failed to save metadata:', storageError);
            // 即使元数据保存失败，也返回成功响应，因为文件已经上传到Telegram
        }        // 返回成功响应（与原项目格式完全一致）
        return res.status(200).json([{ 
            'src': `/file/${fileKey}` 
        }]);

    } catch (error) {
        console.error('Upload error:', error);
        
        // 处理multer错误
        if (error instanceof multer.MulterError) {
            if (error.code === 'LIMIT_FILE_SIZE') {
                return res.status(400).json({ 
                    error: 'File too large. Maximum size is 20MB.' 
                });
            }
            if (error.code === 'LIMIT_FILE_COUNT') {
                return res.status(400).json({ 
                    error: 'Too many files. Only one file allowed per upload.' 
                });
            }
        }
        
        // 处理文件类型错误
        if (error.message.includes('not allowed')) {
            return res.status(400).json({ 
                error: error.message 
            });
        }
        
        // 通用错误响应
        return res.status(500).json({ 
            error: error.message || 'Internal server error' 
        });
    }
});

// 错误处理中间件
router.use((error, req, res, next) => {
    console.error('Upload route error:', error);
    
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({ 
                error: 'File too large. Maximum size is 20MB.' 
            });
        }
        return res.status(400).json({ 
            error: 'File upload error: ' + error.message 
        });
    }
    
    res.status(500).json({ 
        error: 'Internal server error' 
    });
});

module.exports = router;