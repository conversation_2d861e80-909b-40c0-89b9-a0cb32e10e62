#!/bin/bash

# Telegraph-Image Express Production Startup Script

echo "🚀 Starting Telegraph-Image Express in Production Mode..."
echo "=============================================="

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found!"
    echo "📝 Please create .env file with your configuration."
    echo "💡 You can copy from .env.example and modify it."
    exit 1
fi

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing production dependencies..."
    npm ci --only=production
fi

# Set production environment
export NODE_ENV=production

# Start the server
echo "🌟 Starting server in production mode..."
echo "📝 Logs will be displayed below:"
echo "=============================================="

# Use PM2 if available, otherwise use node directly
if command -v pm2 &> /dev/null; then
    echo "🔧 Using PM2 for process management..."
    pm2 start src/app.js --name "telegraph-image" --env production
    pm2 logs telegraph-image
else
    echo "⚠️  PM2 not found, starting with node directly..."
    echo "💡 Consider installing PM2 for better production management: npm install -g pm2"
    node src/app.js
fi